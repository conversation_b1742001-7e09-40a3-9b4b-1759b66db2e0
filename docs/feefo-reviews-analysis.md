# Feefo Reviews Integration Analysis

## Overview

The Bon Voyage site integrates with Feefo reviews through a custom CakePHP datasource that queries the Feefo API directly on-the-fly. Reviews are **not stored locally** - they are fetched from Feefo's XML API each time they're needed.

## Architecture

### 1. Custom DataSource (`app/models/datasources/feefo_source.php`)

The integration uses a custom CakePHP DataSource that acts as an adapter between the application and Feefo's XML API:

- **API Endpoint**: `http://www.feefo.com/feefo/xmlfeed.jsp`
- **Default Logon**: `www.bon-voyage.co.uk`
- **Data Format**: XML responses converted to PHP arrays
- **No Local Storage**: Reviews are fetched directly from API each time

### 2. Model (`app/models/feefo_review.php`)

Simple model that uses the custom datasource:
- Uses `feefo_reviews` database configuration
- `$useTable = false` (no local database table)
- Overrides default `beforeFind` method

### 3. Database Configuration (`app/config/database.php`)

```php
var $feefo_reviews = array(
  'datasource' => 'feefo',
  'logon'      => 'www.bon-voyage.co.uk',
  'api'        => 'http://www.feefo.com/feefo/xmlfeed.jsp'
);
```

## How Reviews Are Retrieved

### API Query Process

1. **URL Construction**: The datasource builds API URLs based on query parameters:
   ```php
   $url = $this->_api . '?logon=' . $this->_logon;
   ```

2. **Mode Selection**:
   - **Service Mode**: `&mode=service` (general company reviews)
   - **Product Mode**: `&vendorref={product_code}&mode=product` (specific product reviews)

3. **XML Parsing**: Uses `SimpleXMLElement` to parse XML response:
   ```php
   $xml = new SimpleXMLElement($url, NULL, true);
   ```

4. **Data Extraction**:
   - Summary data (ratings, counts, averages)
   - Individual feedback items (date, rating, comment, etc.)

### Usage in Controllers

Reviews are fetched via the `_feefoReviews()` method in `app_controller.php`:

```php
private function _feefoReviews() {
    return $this->FeefoReview->find('all', array(
        'conditions' => array(
            'product_code' => $this->data['Setting']['product_code']
        ),
        'limit' => $this->data['Setting']['comments_limit']
    ));
}
```

## Display Implementation

### 1. Main Feefo Element (`app/views/elements/modules/feefo.ctp`)
- Shows Feefo logo/score image via proxy
- Links to full reviews on Feefo site
- Includes microformat data

### 2. Microformat Element (`app/views/elements/feefo_microformat.ctp`)
- Structured data for SEO (Schema.org markup)
- Displays aggregate rating information
- Includes business details

### 3. Widget Integration
- JavaScript widget loaded from `https://api.feefo.com/api/javascript/bon-voyage`
- Iframe implementation in `app/webroot/feefo-iframe.html`

## Potential Issues for Local Development

### 1. **HTTP vs HTTPS**
- API endpoint uses HTTP: `http://www.feefo.com/feefo/xmlfeed.jsp`
- Modern browsers may block mixed content if site runs on HTTPS

### 2. **Network Connectivity**
- Direct API calls require internet connection
- Local development environments may have network restrictions

### 3. **Error Handling**
- The `_api()` method has empty catch block:
  ```php
  } catch (Exception $e) {
      // Empty - no error handling
  }
  ```
- Failed API calls return nothing, causing silent failures

### 4. **XML Parsing Issues**
- `SimpleXMLElement` constructor with URL can fail if:
  - URL is unreachable
  - Response is not valid XML
  - Network timeouts occur

### 5. **Caching**
- No apparent caching mechanism for API responses
- Each page load triggers new API call
- Could cause performance issues and rate limiting

## Debugging Recommendations

### 1. Test API Endpoint Directly
```bash
curl "http://www.feefo.com/feefo/xmlfeed.jsp?logon=www.bon-voyage.co.uk&mode=service&limit=5"
```

### 2. Add Debug Logging
Add logging to the `_api()` method to see what's happening:
```php
private function _api($url) {
    try {
        error_log("Feefo API URL: " . $url);
        $xml = new SimpleXMLElement($url, NULL, true);
        error_log("Feefo API Success");
        // ... rest of method
    } catch (Exception $e) {
        error_log("Feefo API Error: " . $e->getMessage());
        return array('summary' => array(), 'feedback' => array());
    }
}
```

### 3. Check Network Configuration
- Verify local environment can make outbound HTTP requests
- Check if any firewall/proxy settings block the API endpoint
- Test with different network configurations

### 4. Verify Logon Credentials
- Confirm `www.bon-voyage.co.uk` is still the correct logon identifier
- Check if Feefo account is active and configured properly

## Recommendations for Improvement

### 1. **Add Proper Error Handling**
- Log API failures
- Return empty arrays instead of null
- Provide fallback content

### 2. **Implement Caching**
- Cache API responses for 15-30 minutes
- Reduce API calls and improve performance
- Handle cache invalidation properly

### 3. **Add Configuration Options**
- Make API endpoint configurable
- Allow different logon values per environment
- Add timeout settings

### 4. **Consider HTTPS Migration**
- Update to HTTPS API endpoint if available
- Ensure compatibility with modern security requirements

### 5. **Add Monitoring**
- Track API response times
- Monitor success/failure rates
- Alert on extended outages

## Technical Details

### API Response Structure

The Feefo XML API returns data in this structure:

```xml
<FEEDBACKLIST>
  <SUMMARY>
    <MODE>service</MODE>
    <VENDORLOGON>www.bon-voyage.co.uk</VENDORLOGON>
    <COUNT>10</COUNT>
    <AVERAGE>95</AVERAGE>
    <TOTALSERVICECOUNT>1234</TOTALSERVICECOUNT>
    <!-- ... more summary fields -->
  </SUMMARY>
  <FEEDBACK>
    <DATE>2024-01-15</DATE>
    <HREVIEWRATING>5</HREVIEWRATING>
    <SERVICERATING>Excellent</SERVICERATING>
    <CUSTOMERCOMMENT>Great service!</CUSTOMERCOMMENT>
    <READMOREURL>http://...</READMOREURL>
  </FEEDBACK>
  <!-- ... more feedback items -->
</FEEDBACKLIST>
```

### Data Transformation

The datasource converts XML to this PHP array structure:

```php
array(
  'summary' => array(
    'mode' => 'service',
    'vendorlogon' => 'www.bon-voyage.co.uk',
    'count' => '10',
    'average' => '95',
    // ... 20+ summary fields
  ),
  'feedback' => array(
    array(
      'FeefoReview' => array(
        'date' => '2024-01-15',
        'rating' => '5',
        'service' => 'Excellent',
        'comment' => 'Great service!',
        'readmore' => 'http://...'
      )
    ),
    // ... more reviews
  )
)
```

### Current Usage Patterns

1. **Home Page**: Displays widget via iframe (`/feefo-iframe.html`)
2. **General Pages**: Shows Feefo logo/score via proxy (`/proxy/feefo/`)
3. **Microformat Data**: Structured data for SEO in `feefo_microformat.ctp`

### Proxy Implementation

The site uses a proxy for Feefo images:
- URL: `/proxy/feefo/?resource=feefo%2Ffeefologo.jsp%3Flogon%3Dwww.bon-voyage.co.uk%26template%3Dn150x45.png`
- This suggests there may be CORS or caching reasons for proxying

## Troubleshooting Local Issues

### Most Likely Causes for No Reviews on Local:

1. **Network Restrictions**: DDEV/Docker may block outbound HTTP requests
2. **SSL/TLS Issues**: Mixed content blocking (HTTPS site calling HTTP API)
3. **Firewall**: Corporate/local firewall blocking feefo.com
4. **DNS Resolution**: Local DNS not resolving feefo.com correctly
5. **Silent Failures**: Empty catch block hiding actual errors

### Quick Diagnostic Steps:

1. **Test API directly from command line**:
   ```bash
   curl -v "http://www.feefo.com/feefo/xmlfeed.jsp?logon=www.bon-voyage.co.uk&mode=service&limit=1"
   ```

2. **Check from within DDEV container**:
   ```bash
   ddev ssh
   curl "http://www.feefo.com/feefo/xmlfeed.jsp?logon=www.bon-voyage.co.uk&mode=service&limit=1"
   ```

3. **Add temporary debug output** to `feefo_source.php`:
   ```php
   private function _api($url) {
       error_log("Feefo API attempting: " . $url);
       try {
           $xml = new SimpleXMLElement($url, NULL, true);
           error_log("Feefo API success, got XML");
           // ... rest of method
       } catch (Exception $e) {
           error_log("Feefo API failed: " . $e->getMessage());
           return null;
       }
   }
   ```

4. **Check PHP error logs** for any SimpleXMLElement errors

### Expected Behavior:
- API should return XML with reviews
- Reviews should appear in microformat element
- Feefo logo/score should display via proxy

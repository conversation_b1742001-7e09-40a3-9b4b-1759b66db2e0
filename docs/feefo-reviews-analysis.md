# Feefo Reviews Integration Analysis

## Overview

The Bon Voyage site integrates with Feefo reviews through a custom CakePHP datasource that queries the Feefo API directly on-the-fly. Reviews are **not stored locally** - they are fetched from Feefo's XML API each time they're needed.

## Architecture

### 1. Custom DataSource (`app/models/datasources/feefo_source.php`)

The integration uses a custom CakePHP DataSource that acts as an adapter between the application and Feefo's XML API:

- **API Endpoint**: `http://www.feefo.com/feefo/xmlfeed.jsp`
- **Default Logon**: `www.bon-voyage.co.uk`
- **Data Format**: XML responses converted to PHP arrays
- **No Local Storage**: Reviews are fetched directly from API each time

### 2. Model (`app/models/feefo_review.php`)

Simple model that uses the custom datasource:
- Uses `feefo_reviews` database configuration
- `$useTable = false` (no local database table)
- Overrides default `beforeFind` method

### 3. Database Configuration (`app/config/database.php`)

```php
var $feefo_reviews = array(
  'datasource' => 'feefo',
  'logon'      => 'www.bon-voyage.co.uk',
  'api'        => 'http://www.feefo.com/feefo/xmlfeed.jsp'
);
```

## How Reviews Are Retrieved

### API Query Process

1. **URL Construction**: The datasource builds API URLs based on query parameters:
   ```php
   $url = $this->_api . '?logon=' . $this->_logon;
   ```

2. **Mode Selection**:
   - **Service Mode**: `&mode=service` (general company reviews)
   - **Product Mode**: `&vendorref={product_code}&mode=product` (specific product reviews)

3. **XML Parsing**: Uses `SimpleXMLElement` to parse XML response:
   ```php
   $xml = new SimpleXMLElement($url, NULL, true);
   ```

4. **Data Extraction**:
   - Summary data (ratings, counts, averages)
   - Individual feedback items (date, rating, comment, etc.)

### Usage in Controllers

Reviews are fetched via the `_feefoReviews()` method in `app_controller.php`:

```php
private function _feefoReviews() {
    return $this->FeefoReview->find('all', array(
        'conditions' => array(
            'product_code' => $this->data['Setting']['product_code']
        ),
        'limit' => $this->data['Setting']['comments_limit']
    ));
}
```

## Display Implementation

### 1. Main Feefo Element (`app/views/elements/modules/feefo.ctp`)
- Shows Feefo logo/score image via proxy
- Links to full reviews on Feefo site
- Includes microformat data

### 2. Microformat Element (`app/views/elements/feefo_microformat.ctp`)
- Structured data for SEO (Schema.org markup)
- Displays aggregate rating information
- Includes business details

### 3. Widget Integration
- JavaScript widget loaded from `https://api.feefo.com/api/javascript/bon-voyage`
- Iframe implementation in `app/webroot/feefo-iframe.html`

## Potential Issues for Local Development

### 1. **HTTP vs HTTPS**
- API endpoint uses HTTP: `http://www.feefo.com/feefo/xmlfeed.jsp`
- Modern browsers may block mixed content if site runs on HTTPS

### 2. **Network Connectivity**
- Direct API calls require internet connection
- Local development environments may have network restrictions

### 3. **Error Handling**
- The `_api()` method has empty catch block:
  ```php
  } catch (Exception $e) {
      // Empty - no error handling
  }
  ```
- Failed API calls return nothing, causing silent failures

### 4. **XML Parsing Issues**
- `SimpleXMLElement` constructor with URL can fail if:
  - URL is unreachable
  - Response is not valid XML
  - Network timeouts occur

### 5. **Caching**
- No apparent caching mechanism for API responses
- Each page load triggers new API call
- Could cause performance issues and rate limiting

## Debugging Recommendations

### 1. Test API Endpoint Directly
```bash
curl "http://www.feefo.com/feefo/xmlfeed.jsp?logon=www.bon-voyage.co.uk&mode=service&limit=5"
```

### 2. Add Debug Logging
Add logging to the `_api()` method to see what's happening:
```php
private function _api($url) {
    try {
        error_log("Feefo API URL: " . $url);
        $xml = new SimpleXMLElement($url, NULL, true);
        error_log("Feefo API Success");
        // ... rest of method
    } catch (Exception $e) {
        error_log("Feefo API Error: " . $e->getMessage());
        return array('summary' => array(), 'feedback' => array());
    }
}
```

### 3. Check Network Configuration
- Verify local environment can make outbound HTTP requests
- Check if any firewall/proxy settings block the API endpoint
- Test with different network configurations

### 4. Verify Logon Credentials
- Confirm `www.bon-voyage.co.uk` is still the correct logon identifier
- Check if Feefo account is active and configured properly

## Recommendations for Improvement

### 1. **Add Proper Error Handling**
- Log API failures
- Return empty arrays instead of null
- Provide fallback content

### 2. **Implement Caching**
- Cache API responses for 15-30 minutes
- Reduce API calls and improve performance
- Handle cache invalidation properly

### 3. **Add Configuration Options**
- Make API endpoint configurable
- Allow different logon values per environment
- Add timeout settings

### 4. **Consider HTTPS Migration**
- Update to HTTPS API endpoint if available
- Ensure compatibility with modern security requirements

### 5. **Add Monitoring**
- Track API response times
- Monitor success/failure rates
- Alert on extended outages

## Technical Details

### API Response Structure

The Feefo XML API returns data in this structure:

```xml
<FEEDBACKLIST>
  <SUMMARY>
    <MODE>service</MODE>
    <VENDORLOGON>www.bon-voyage.co.uk</VENDORLOGON>
    <COUNT>10</COUNT>
    <AVERAGE>95</AVERAGE>
    <TOTALSERVICECOUNT>1234</TOTALSERVICECOUNT>
    <!-- ... more summary fields -->
  </SUMMARY>
  <FEEDBACK>
    <DATE>2024-01-15</DATE>
    <HREVIEWRATING>5</HREVIEWRATING>
    <SERVICERATING>Excellent</SERVICERATING>
    <CUSTOMERCOMMENT>Great service!</CUSTOMERCOMMENT>
    <READMOREURL>http://...</READMOREURL>
  </FEEDBACK>
  <!-- ... more feedback items -->
</FEEDBACKLIST>
```

### Data Transformation

The datasource converts XML to this PHP array structure:

```php
array(
  'summary' => array(
    'mode' => 'service',
    'vendorlogon' => 'www.bon-voyage.co.uk',
    'count' => '10',
    'average' => '95',
    // ... 20+ summary fields
  ),
  'feedback' => array(
    array(
      'FeefoReview' => array(
        'date' => '2024-01-15',
        'rating' => '5',
        'service' => 'Excellent',
        'comment' => 'Great service!',
        'readmore' => 'http://...'
      )
    ),
    // ... more reviews
  )
)
```

### Current Usage Patterns

1. **Home Page**: Displays widget via iframe (`/feefo-iframe.html`)
2. **General Pages**: Shows Feefo logo/score via proxy (`/proxy/feefo/`)
3. **Microformat Data**: Structured data for SEO in `feefo_microformat.ctp`

### Proxy Implementation

The site uses a proxy for Feefo images:
- URL: `/proxy/feefo/?resource=feefo%2Ffeefologo.jsp%3Flogon%3Dwww.bon-voyage.co.uk%26template%3Dn150x45.png`
- This suggests there may be CORS or caching reasons for proxying

## Troubleshooting Local Issues

### Most Likely Causes for No Reviews on Local:

1. **Network Restrictions**: DDEV/Docker may block outbound HTTP requests
2. **SSL/TLS Issues**: Mixed content blocking (HTTPS site calling HTTP API)
3. **Firewall**: Corporate/local firewall blocking feefo.com
4. **DNS Resolution**: Local DNS not resolving feefo.com correctly
5. **Silent Failures**: Empty catch block hiding actual errors

### Quick Diagnostic Steps:

1. **Test API directly from command line**:
   ```bash
   curl -v "http://www.feefo.com/feefo/xmlfeed.jsp?logon=www.bon-voyage.co.uk&mode=service&limit=1"
   ```

2. **Check from within DDEV container**:
   ```bash
   ddev ssh
   curl "http://www.feefo.com/feefo/xmlfeed.jsp?logon=www.bon-voyage.co.uk&mode=service&limit=1"
   ```

3. **Add temporary debug output** to `feefo_source.php`:
   ```php
   private function _api($url) {
       error_log("Feefo API attempting: " . $url);
       try {
           $xml = new SimpleXMLElement($url, NULL, true);
           error_log("Feefo API success, got XML");
           // ... rest of method
       } catch (Exception $e) {
           error_log("Feefo API failed: " . $e->getMessage());
           return null;
       }
   }
   ```

4. **Check PHP error logs** for any SimpleXMLElement errors

### Expected Behavior:
- API should return XML with reviews
- Reviews should appear in microformat element
- Feefo logo/score should display via proxy

## 🚨 CRITICAL: Current API Implementation is OUTDATED

### Analysis of Current vs Modern Feefo API

**Current Implementation Issues:**
1. **Deprecated XML API**: Using old `http://www.feefo.com/feefo/xmlfeed.jsp`
2. **HTTP Only**: No HTTPS support in legacy endpoint
3. **Limited Parameters**: Only supports basic `logon`, `mode`, `vendorref`, `limit`
4. **No Authentication**: Cannot access enhanced data or private reviews
5. **XML Parsing**: Manual XML parsing vs modern JSON responses

**Modern Feefo API v20 (Current Standard):**
- **Base URL**: `https://api.feefo.com/api/20/reviews/{mode}`
- **Format**: JSON responses (much easier to parse)
- **HTTPS**: Secure connections
- **Enhanced Data**: Rich metadata, media, sentiment analysis
- **Authentication**: OAuth 2.0 for private data access

## Recommended Modern Implementation

### 1. Updated API Endpoints

Replace the current XML endpoint with modern JSON API:

```php
// OLD (current implementation)
private $_api = 'http://www.feefo.com/feefo/xmlfeed.jsp';

// NEW (recommended)
private $_api = 'https://api.feefo.com/api/20/reviews';
```

### 2. Modern API Structure

**Service Reviews:**
```
GET https://api.feefo.com/api/20/reviews/service?merchant_identifier=bon-voyage
```

**Product Reviews:**
```
GET https://api.feefo.com/api/20/reviews/product?merchant_identifier=bon-voyage&sku=PRODUCT_SKU
```

**All Reviews:**
```
GET https://api.feefo.com/api/20/reviews/all?merchant_identifier=bon-voyage
```

### 3. Key Parameter Changes

| Old Parameter | New Parameter | Notes |
|---------------|---------------|-------|
| `logon=www.bon-voyage.co.uk` | `merchant_identifier=bon-voyage` | Cleaner identifier format |
| `mode=service` | `/service` endpoint | Mode is now part of URL path |
| `vendorref=SKU` | `sku=SKU` | More intuitive parameter name |
| `limit=10` | `page_size=10` | Supports pagination |
| N/A | `page=1` | Pagination support |

### 4. Modern JSON Response Structure

The new API returns clean JSON instead of XML:

```json
{
  "summary": {
    "meta": {
      "count": 1234,
      "pages": 62,
      "page_size": 20,
      "current_page": 1
    }
  },
  "reviews": [
    {
      "merchant": {
        "identifier": "bon-voyage"
      },
      "customer": {
        "display_name": "John D."
      },
      "service": {
        "rating": {
          "min": 1,
          "max": 5,
          "rating": 5
        },
        "title": "Excellent service",
        "review": "Great experience with Bon Voyage...",
        "created_at": 1640995200000
      }
    }
  ]
}
```

## Proposed Modern Implementation

### 1. Updated FeefoSource Class

Here's how to modernize the datasource:

```php
<?php
class FeefoSource extends DataSource {

    private $_merchant_identifier = 'bon-voyage'; // Updated identifier
    private $_api = 'https://api.feefo.com/api/20/reviews'; // Modern API
    private $_timeout = 30; // Add timeout handling

    public function read($model, $queryData = array()) {

        // Build modern API URL
        $mode = !empty($queryData['conditions']['product_code']) ? 'product' : 'service';
        $url = $this->_api . '/' . $mode;

        // Build query parameters
        $params = array(
            'merchant_identifier' => $this->_merchant_identifier,
            'page_size' => isset($queryData['limit']) ? $queryData['limit'] : 20,
            'page' => 1
        );

        // Add product SKU if specified
        if (!empty($queryData['conditions']['product_code'])) {
            $params['sku'] = $queryData['conditions']['product_code'];
        }

        $url .= '?' . http_build_query($params);

        return $this->_apiCall($url);
    }

    private function _apiCall($url) {
        try {
            // Use cURL for better error handling and HTTPS support
            $ch = curl_init();
            curl_setopt_array($ch, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => $this->_timeout,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_USERAGENT => 'Bon Voyage Website/1.0'
            ));

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                error_log("Feefo API cURL Error: " . $error);
                return $this->_emptyResponse();
            }

            if ($httpCode !== 200) {
                error_log("Feefo API HTTP Error: " . $httpCode . " for URL: " . $url);
                return $this->_emptyResponse();
            }

            $data = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log("Feefo API JSON Error: " . json_last_error_msg());
                return $this->_emptyResponse();
            }

            return $this->_transformResponse($data);

        } catch (Exception $e) {
            error_log("Feefo API Exception: " . $e->getMessage());
            return $this->_emptyResponse();
        }
    }

    private function _transformResponse($data) {
        $reviews = array();

        if (isset($data['reviews']) && is_array($data['reviews'])) {
            foreach ($data['reviews'] as $review) {
                if (isset($review['service'])) {
                    $reviews[] = array(
                        'FeefoReview' => array(
                            'date' => date('Y-m-d', $review['service']['created_at'] / 1000),
                            'rating' => $review['service']['rating']['rating'],
                            'service' => $this->_getRatingText($review['service']['rating']['rating']),
                            'comment' => $review['service']['review'] ?? '',
                            'title' => $review['service']['title'] ?? '',
                            'customer' => $review['customer']['display_name'] ?? 'Anonymous'
                        )
                    );
                }
            }
        }

        return array(
            'summary' => $this->_buildSummary($data),
            'feedback' => $reviews
        );
    }

    private function _buildSummary($data) {
        $meta = $data['summary']['meta'] ?? array();

        return array(
            'mode' => 'service',
            'count' => $meta['count'] ?? 0,
            'pages' => $meta['pages'] ?? 0,
            'page_size' => $meta['page_size'] ?? 20,
            'current_page' => $meta['current_page'] ?? 1
        );
    }

    private function _getRatingText($rating) {
        $ratings = array(
            5 => 'Excellent',
            4 => 'Good',
            3 => 'Average',
            2 => 'Poor',
            1 => 'Bad'
        );
        return $ratings[$rating] ?? 'Unknown';
    }

    private function _emptyResponse() {
        return array(
            'summary' => array(
                'count' => 0,
                'pages' => 0,
                'page_size' => 20,
                'current_page' => 1
            ),
            'feedback' => array()
        );
    }
}
```

### 2. Updated Database Configuration

```php
var $feefo_reviews = array(
    'datasource' => 'feefo',
    'merchant_identifier' => 'bon-voyage', // Updated parameter
    'api' => 'https://api.feefo.com/api/20/reviews', // HTTPS endpoint
    'timeout' => 30
);
```

### 3. Benefits of Modern Implementation

1. **HTTPS Support**: Secure connections, no mixed content issues
2. **Better Error Handling**: Proper logging and fallback responses
3. **JSON Parsing**: Native PHP support, no XML complexity
4. **Timeout Handling**: Prevents hanging requests
5. **Pagination Support**: Handle large review sets efficiently
6. **Enhanced Data**: Access to customer names, titles, timestamps
7. **Future-Proof**: Uses current API version with ongoing support

### 4. Migration Steps

1. **Test New Endpoint**: Verify `https://api.feefo.com/api/20/reviews/service?merchant_identifier=bon-voyage` works
2. **Update Datasource**: Replace `feefo_source.php` with modern implementation
3. **Update Configuration**: Change database config to use new parameters
4. **Test Locally**: Verify reviews load correctly
5. **Update Views**: Utilize new data fields (customer names, titles, etc.)
6. **Add Caching**: Implement response caching for performance

### 5. Immediate Action Required

The current XML API may be deprecated or have limited functionality. The modern JSON API is the recommended approach and should resolve the local development issues you're experiencing.
```

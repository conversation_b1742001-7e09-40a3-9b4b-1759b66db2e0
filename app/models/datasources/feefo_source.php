<?php

class FeefoSource extends DataSource {

  protected $_schema = array(
    'reviews' => array(
      'feedbackid' => array(
        'type' => 'integer',
        'null' => true,
        'key' => 'primary',
        'length' => 11,
      ),
      'date' => array(
        'type' => 'string',
        'null' => false,
        'length' => 11
      ),
      'description' => array(
        'type' => 'string',
        'null' => true
      ),
      'rating' => array(
        'type' => 'string',
        'null' => true
      ),
      'service' => array(
        'type' => 'string',
        'null' => true
      ),
      'comment' => array(
        'type' => 'string',
        'null' => true
      ),
      'readmore' => array(
        'type' => 'string',
        'null' => true
      ),
    )
  );

  private $_merchant_identifier = 'bon-voyage';

  private $_api = 'https://api.feefo.com/api/20/reviews/summary';

  private $_timeout = 30;

  public function listSources($data = null) {
    return array('reviews');
  }

  public function __construct($config) {
    // set merchant identifier
    if (!empty($config['merchant_identifier'])) {
      $this->_merchant_identifier = $config['merchant_identifier'];
    }

    // set the api endpoint
    if (!empty($config['api'])) {
      $this->_api = $config['api'];
    }

    // set timeout
    if (!empty($config['timeout'])) {
      $this->_timeout = $config['timeout'];
    }

    parent::__construct($config);
  }

  public function read(&$model, $queryData = array()) {
    // Use modern Summary API - it's all we need!
    $mode = !empty($queryData['conditions']['product_code']) ? 'product' : 'service';
    $url = $this->_api . '/' . $mode;

    $params = array(
      'merchant_identifier' => $this->_merchant_identifier
    );

    if (!empty($queryData['conditions']['product_code'])) {
      $params['sku'] = $queryData['conditions']['product_code'];
    }

    $url .= '?' . http_build_query($params);

    $data = $this->_apiCall($url);

    if (!$data) {
      return $this->_emptyResponse();
    }

    return $this->_transformResponse($data);
  }

  public function describe($model) {
    return $this->_schema['reviews'];
  }

  public function getLogon() {
    return $this->_merchant_identifier;
  }

  private function _apiCall($url) {
    try {
      $ch = curl_init();
      curl_setopt_array($ch, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => $this->_timeout,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_USERAGENT => 'Bon Voyage Website/1.0'
      ));

      $response = curl_exec($ch);
      $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
      $error = curl_error($ch);
      curl_close($ch);

      if ($error) {
        error_log("Feefo API cURL Error: " . $error);
        return null;
      }

      if ($httpCode !== 200) {
        error_log("Feefo API HTTP Error: " . $httpCode . " for URL: " . $url);
        return null;
      }

      $data = json_decode($response, true);

      if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("Feefo API JSON Error: " . json_last_error_msg());
        return null;
      }

      error_log("Feefo API Success: " . $url);
      return $data;

    } catch (Exception $e) {
      error_log("Feefo API Exception: " . $e->getMessage());
      return null;
    }
  }

  private function _transformResponse($data) {
    // Transform modern Summary API response to match expected structure
    $rating = isset($data['rating']) ? $data['rating'] : array();
    $meta = isset($data['meta']) ? $data['meta'] : array();
    $service = isset($rating['service']) ? $rating['service'] : array();

    return array(
      'summary' => array(
        // Required for microformat view
        'title' => 'Bon Voyage Travel & Tours Ltd',
        'average' => isset($rating['rating']) ? $rating['rating'] : 0,
        'count' => isset($meta['count']) ? $meta['count'] : 0,

        // Backward compatibility fields
        'mode' => 'service',
        'best' => isset($rating['max']) ? $rating['max'] : 5,
        'worst' => isset($rating['min']) ? $rating['min'] : 1,
        'totalservicecount' => isset($meta['count']) ? $meta['count'] : 0,
        'totalproductcount' => 0,

        // Rating distribution (from service.5_star, service.4_star, etc.)
        'serviceexcellent' => isset($service['5_star']) ? $service['5_star'] : 0,
        'servicegood' => isset($service['4_star']) ? $service['4_star'] : 0,
        'servicepoor' => isset($service['2_star']) ? $service['2_star'] : 0,
        'servicebad' => isset($service['1_star']) ? $service['1_star'] : 0,
      ),
      'feedback' => array() // Not needed - views don't use individual reviews
    );
  }

  private function _emptyResponse() {
    return array(
      'summary' => array(
        'title' => 'Bon Voyage Travel & Tours Ltd',
        'average' => 0,
        'count' => 0,
        'mode' => 'service',
        'best' => 5,
        'worst' => 1,
        'totalservicecount' => 0,
        'totalproductcount' => 0
      ),
      'feedback' => array()
    );
  }
}

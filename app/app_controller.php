<?php
require(APP.'base_controller.php');
class AppController extends BaseController {

    var $selectedPrimaryNav;

    public $criticalCss = 'destinations';

    function beforeFilter() {

        parent::beforeFilter();

        $cookiePrefs = $this->Cookie->read('cookie_pref');

        $feefoReviews = $this->cacher('feefo_reviews', function () {
            return $this->_feefoReviews();
        });

        if (Configure::read('Runtime.site_or_admin') != 'admin' &&
        Configure::read('debug') == 0 && $this->action !== 'healthcheck') {

            $this->Security->blackHoleCallback = 'forceSSL';
            if(Configure::read('forceSSL') === true) {
                $this->Security->requireSecure();
            }

        } else {
            $this->Security->enabled = false;
        }

        if (!$this->Session->check('WebSource')) {

            if (!empty($_COOKIE['__utmz']) && preg_match('/utmgclid/', $_COOKIE['__utmz'])) {
                $this->Session->write('WebSource', 'PPC');

            } elseif (!empty($_COOKIE['__utmz']) && preg_match('/utmcsr=(ENews_[^|]*)/', $_COOKIE['__utmz'], $matches)) {

                $this->Session->write('WebSource',  $matches[1]);

                //If querystring param ‘gclid’ is set
            } elseif (!empty($_GET['gclid']) && $_GET['gclid']) {
                //Set ‘WebSource’ to ‘PPC’
                $this->Session->write('WebSource',  'PPC');

                //If querystring param ‘utm_source’ is set AND its value starts with ‘ENews_’
            } elseif (!empty($_GET['utm_source']) && preg_match('/(ENews_[^|]*)/', $_GET['utm_source'], $matches)) {

                //Set ‘WebSource’ to <value of utm_source param>, e.g. ‘ENews_Feb_10_-_Family_Holidays’
                $this->Session->write('WebSource',  $matches[1]);

            } else {
                $this->Session->write('WebSource', 'Other');
            }
        }

        if ($websource = $this->Session->read('WebSource')) {
            $phoneSwitch = '';
            if ($websource == 'PPC') {
                $phoneSwitch = 'ppc';
            } elseif (preg_match('/^ENews_/', $websource)) {
                $phoneSwitch = 'enews';
            }
            $this->set(compact('phoneSwitch'));
        }

        $relatedSections = array(
            'Itineraries' => array(
                'label'      => 'Sample <br>Itineraries',
                'plugin'     => null,
                'controller' => 'itineraries',
                'action'     => 'index',
                'enabled'    => true,
            ),
            'Activities' => array(
                'label'      => 'What To <br>See And Do',
                'plugin'     => null,
                'controller' => 'activities',
                'action'     => 'index',
                'enabled'    => true,
            ),
            'Accommodation' => array(
                'label'      => 'Places <br>To Stay',
                'plugin'     => null,
                'controller' => 'accommodations',
                'action'     => 'index',
                'enabled'    => true,
            ),
            'Videos' => array(
                'label'      => 'Movie <br>Gallery',
                'plugin'     => 'gdata',
                'controller' => 'youtube_videos',
                'action'     => 'index',
                'enabled'    => false,
            ),
            'Images' => array(
                'label'      => 'Image <br>Gallery',
                'plugin'     => null,
                'controller' => 'images',
                'action'     => 'index',
                'enabled'    => false,
            )
        );

        $this->set(compact('cookiePrefs', 'feefoReviews', 'relatedSections'));
    }

    function beforeRender() {

        parent::beforeRender();

        $selectedPrimaryNav = $this->selectedPrimaryNav;
        $criticalCss = $this->criticalCss;

        // Load navigation data for all pages (like the endpoints do)
        // Only load if Navigation component is available and we're not in admin
        if (isset($this->Navigation) && Configure::read('Runtime.site_or_admin') != 'admin') {
            try {
                $navigationData = $this->Navigation->getNavigationData();
                if ($navigationData) {
                    // Extract navigation components (same as endpoints)
                    $mainNav = $navigationData['mainNav'];
                    $usaDestinations = $navigationData['usaDestinations'];
                    $canadaDestinations = $navigationData['canadaDestinations'];
                    $holidayTypes = $navigationData['holidayTypes'];
                    $whatsHot = $navigationData['whatsHot'];
                    $holidayInfoPages = $navigationData['holidayInfoPages'];
                    $aboutPages = $navigationData['aboutPages'];

                    // Set variables for the view (same as endpoints)
                    // Don't override holidayTypes if we're in HolidayTypesController (it has its own data)
                    if ($this->name !== 'HolidayTypes') {
                        $this->set(compact('mainNav', 'usaDestinations', 'canadaDestinations', 'holidayTypes', 'whatsHot', 'holidayInfoPages', 'aboutPages'));
                    } else {
                        $this->set(compact('mainNav', 'usaDestinations', 'canadaDestinations', 'whatsHot', 'holidayInfoPages', 'aboutPages'));
                    }
                }
            } catch (Exception $e) {
                // Silently fail if navigation data can't be loaded - don't break the page
                // This ensures other controllers without Navigation component still work
            }
        }

        $this->set(compact(
            'selectedPrimaryNav',
            'criticalCss'
        ));

    }

    function forceSSL() {
        // dont lose tracking
        $this->log('forceSSL called, returning instead', 'debug');
        return;

        $get = array();
        $url = $this->here;

        foreach ($_GET as $i => $v) {

            if ($i == 'url') {
                continue;
            }

            $get[] = $i . '=' . $v;

        }

        if (!empty($get))
        {
            $url .= '?' . implode('&', $get);
        }

        // now redirect
        // if (!$this->request->is('ssl')) {
            $this->redirect('https://' . env('SERVER_NAME') . $url, 307);
        // } else return;
    }

    /**
    * Find the canonical url for the current record
    */
    protected function _canonicalUrl($params = array(), $path = null) {
        $path = $path ? $path : $this->name;

        // Check if this is not the canonical page
        if (preg_match('/^\/' . $path . '\/?$/i', $this->here) == 0) {

            // Generate the canonical URL
            $canonicalUrl = Router::url($params, true);

            // Check if the request is secure (HTTPS)
            $isHttps = false;

            // Check for the presence of the X-Forwarded-Proto header
            if (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
                $isHttps = true; // Load balanced and secure
            }
            // Additionally check if HTTPS is set directly
            elseif (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
                $isHttps = true; // Direct HTTPS request
            }

            // Set the canonical URL based on HTTPS check
            if ($isHttps) {
                // Ensure the URL is generated with HTTPS
                if (strpos($canonicalUrl, 'http://') === 0) {
                    $canonicalUrl = str_replace('http://', 'https://', $canonicalUrl);
                }
            } else {
                // If not HTTPS, ensure the URL uses HTTP
                if (strpos($canonicalUrl, 'https://') === 0) {
                    $canonicalUrl = str_replace('https://', 'http://', $canonicalUrl);
                }
            }

            // Set the canonical URL
            $this->set('canonicalUrl', $canonicalUrl);
        }
    }

    /**
    * Set the canonical url for paginated pages
    */
    protected function _canonicalUrlForPaginated()
    {
        if (empty($this->params['named']['page'])) {
            return;
        }

        $urlParams = $this->params;
        unset(
            $urlParams['form'],
            $urlParams['isAjax'],
            $urlParams['named'],
            $urlParams['paging'],
            $urlParams['pass'],
            $urlParams['url'],
            $urlParams['_Token']
        );

        $this->_canonicalUrl($urlParams);
    }

    /**
    * Attempts to retrieve data for the given $key from the cache.
    * On failure, calls the passed function and writes the result
    * to cache and then returns it
    */
    public function cacher($key, $func) {

        if (($data = Cache::read($key, 'data')) === false) {
            $data = $func();
            Cache::write($key, $data, 'data');
        }

        return $data;
    }

    /**
    * Sets the reviews for the Feefo module
    **/
    private function _feefoReviews() {
        error_log("DEBUG: _feefoReviews() called");

        // instantiate feefo review model
        if (empty($this->FeefoReview)) {
            App::import('Model', 'FeefoReview');
            $this->loadModel('FeefoReview');
        }

        // get the datasource logon - needed to generate correct links in the view
        $this->set('feefo_logon', $this->FeefoReview->getDataSource()->getLogon());

        if (!isset($this->data['Setting'])) {
            error_log("DEBUG: No Setting data, returning empty array");
            return [];
        }

        error_log("DEBUG: Calling FeefoReview->find()");
        // get reviews
        $result = $this->FeefoReview->find('all', array(
            'conditions' => array(
                'product_code' => $this->data['Setting']['product_code']
            ),
            'limit' => $this->data['Setting']['comments_limit']
        ));

        error_log("DEBUG: FeefoReview->find() result: " . print_r($result, true));
        return $result;
    }

    protected function _subscribe($email, $firstName, $lastName) {
        App::import('Core', 'HttpSocket');
        $Http = new HttpSocket();
        $data = array(
            'email'      => $email,
            'first_name' => $firstName,
            'last_name'  => $lastName,
            'url'        => 'https://www.bon-voyage.co.uk',
            'client_ip'  => isset($_SERVER['HTTP_X_FORWARDED_FOR']) ? $_SERVER['HTTP_X_FORWARDED_FOR'] : $_SERVER['REMOTE_ADDR'],
            'GCLID' => isset($_GET['gclid']) ? $_GET['gclid'] : ''
        );

        $Http->post('https://email.bon-voyage.co.uk/subscribe2.aspx', $data);
        return $Http->response['status']['code'];
    }

    protected function _validateEmail($email) {
        App::import('Core', 'HttpSocket');
        $Http = new HttpSocket();
        $res = $Http->post('https://email.bon-voyage.co.uk/CheckAddress.aspx', array(
            'email' => $email,
        ));

        return $Http->response['status']['code'] == 200;
    }
}
